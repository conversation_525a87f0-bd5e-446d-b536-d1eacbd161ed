const API_URL = import.meta.env.VITE_REACT_APP_API_URL ||
  (import.meta.env.DEV ? '/api' : 'https://laravel-api.fly.dev/api');

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Promotions
export async function fetchPromotions(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);
    if (params.statut) queryParams.append('statut', params.statut);
    if (params.type) queryParams.append('type', params.type);
    if (params.date_debut) queryParams.append('date_debut', params.date_debut);
    if (params.date_fin) queryParams.append('date_fin', params.date_fin);

    const url = `${API_URL}/promotions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching promotions from:', url);

    const res = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch promotions. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned promotions response:', response);

    return response;
  } catch (error) {
    console.error('Error fetching promotions:', error);
    throw error;
  }
}

export async function fetchPromotionById(id) {
  try {
    const res = await fetch(`${API_URL}/promotions/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch promotion ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log(`API returned promotion ${id} response:`, response);

    return response;
  } catch (error) {
    console.error(`Error fetching promotion ${id}:`, error);
    throw error;
  }
}

export async function createPromotion(data) {
  try {
    console.log('Creating promotion with data:', JSON.stringify(data, null, 2));
    console.log('API URL:', `${API_URL}/promotions`);
    console.log('Headers:', getAuthHeaders());

    const res = await fetch(`${API_URL}/promotions`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });

    console.log('Response status:', res.status);
    console.log('Response headers:', Object.fromEntries(res.headers.entries()));

    if (!res.ok) {
      const errorText = await res.text();
      console.error('Server error response:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
        console.error('Parsed error data:', errorData);
      } catch (e) {
        console.error('Could not parse error response as JSON');
        errorData = { message: errorText };
      }

      // Show more detailed error information
      if (errorData.errors) {
        const errorMessages = Object.entries(errorData.errors)
          .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
          .join('; ');
        throw new Error(`Validation errors: ${errorMessages}`);
      }

      throw new Error(errorData.message || `Server Error (${res.status}): ${errorText}`);
    }

    const response = await res.json();
    console.log('Success response:', response);
    return response;
  } catch (error) {
    console.error('Error creating promotion:', error);
    throw error;
  }
}

export async function updatePromotion(id, data) {
  try {
    const res = await fetch(`${API_URL}/promotions/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la mise à jour de la promotion');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error updating promotion:', error);
    throw error;
  }
}

export async function deletePromotion(id) {
  try {
    const res = await fetch(`${API_URL}/promotions/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la suppression de la promotion');
    }

    return res.json();
  } catch (error) {
    console.error('Error deleting promotion:', error);
    throw error;
  }
}

// Discount Rules (Règles de Remise)
export async function fetchDiscountRules(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.type_client) queryParams.append('type_client', params.type_client);
    if (params.active) queryParams.append('active', params.active);

    const url = `${API_URL}/regle-remises${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching discount rules from:', url);

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to fetch discount rules. Status: ${res.status}`);
    }

    const response = await res.json();
    console.log('API returned discount rules response:', response);

    return response;
  } catch (error) {
    console.error('Error fetching discount rules:', error);
    throw error;
  }
}

export async function fetchDiscountRuleById(id) {
  try {
    const res = await fetch(`${API_URL}/regle-remises/${id}`);
    if (!res.ok) {
      throw new Error(`Failed to fetch discount rule ${id}. Status: ${res.status}`);
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error(`Error fetching discount rule ${id}:`, error);
    throw error;
  }
}

export async function createDiscountRule(data) {
  try {
    const res = await fetch(`${API_URL}/regle-remises`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la création de la règle de remise');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error creating discount rule:', error);
    throw error;
  }
}

export async function updateDiscountRule(id, data) {
  try {
    const res = await fetch(`${API_URL}/regle-remises/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la mise à jour de la règle de remise');
    }

    const response = await res.json();
    return response;
  } catch (error) {
    console.error('Error updating discount rule:', error);
    throw error;
  }
}

export async function deleteDiscountRule(id) {
  try {
    const res = await fetch(`${API_URL}/regle-remises/${id}`, {
      method: 'DELETE'
    });

    if (!res.ok) {
      throw new Error('Erreur lors de la suppression de la règle de remise');
    }

    return res.json();
  } catch (error) {
    console.error('Error deleting discount rule:', error);
    throw error;
  }
}
